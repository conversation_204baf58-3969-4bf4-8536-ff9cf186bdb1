import { useRef, useState, useEffect } from 'react'
import { useToast } from '../contexts/ToastContext'

function Camera({ onClose, onCapture, onPhotoComplete, menuLabel }) {
  const { showToast } = useToast()
  const videoRef = useRef(null)
  const [stream, setStream] = useState(null)
  const [countdown, setCountdown] = useState(5)
  const [isCountingDown, setIsCountingDown] = useState(false)
  const [selectedCamera, setSelectedCamera] = useState(null)
  const [mode, setMode] = useState('document')
  const [showPreview, setShowPreview] = useState(false)
  const [previewImage, setPreviewImage] = useState(null)
  const [processing, setProcessing] = useState(false) // 添加处理状态
  const [autoSwitchTimer, setAutoSwitchTimer] = useState(null) // 自动切换定时器引用

  // 身份证拍照子模式状态
  const [idCardMode, setIdCardMode] = useState('front') // 'front' 或 'back'

  // 添加拍照记录状态 - 扩展身份证正反面
  const [photosTaken, setPhotosTaken] = useState({
    document: false,
    'idcard-front': false,
    'idcard-back': false,
    portrait: false
  })

  // 检查是否所有照片都已拍摄 - 身份证需要正反面都完成
  const allPhotosTaken = Object.values(photosTaken).every((taken) => taken)

  // 定义不同模式的裁剪框样式
  const cropStyles = {
    'idcard-front': {
      width: '60%', // 身份证比例约为 1.6:1，适合横向拍摄
      height: '38%',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    },
    'idcard-back': {
      width: '60%', // 身份证背面同样比例
      height: '38%',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    },
    portrait: {
      width: '50%', // 证件照比例约为 0.75:1
      height: '90%',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    }
  }

  // 修改视频容器样式
  const videoContainerStyle = {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: '0.75rem',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    border: '4px solid #14b8a6'
  }

  // 修改视频样式
  const videoStyle = {
    width: '100%',
    height: '100%',
    objectFit: 'contain'
  }

  const setupCamera = async (deviceId = null, mode = 'document') => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      const videoDevices = devices.filter((device) => device.kind === 'videoinput')

      let targetDevice = deviceId
      if (!targetDevice) {
        if (videoDevices.length === 1) {
          targetDevice = videoDevices[0].deviceId
        } else {
          if (mode === 'portrait') {
            targetDevice =
              videoDevices.find((d) => d.label.includes('USB'))?.deviceId ||
              videoDevices[0].deviceId
          } else {
            targetDevice =
              videoDevices.find((d) => d.label.startsWith('ZJCX'))?.deviceId ||
              videoDevices[0].deviceId
          }
        }
      }

      if (targetDevice) {
        if (stream) {
          stream.getTracks().forEach((track) => track.stop())
        }

        // 优化：根据模式调整分辨率
        const resolutionSettings =
          mode === 'document'
            ? { width: { ideal: 1920 }, height: { ideal: 1080 } }
            : { width: { ideal: 1280 }, height: { ideal: 720 } }

        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: {
            deviceId: targetDevice,
            ...resolutionSettings,
            frameRate: { ideal: 24 } // 降低帧率提升性能
          }
        })

        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream
          videoRef.current.onloadedmetadata = () => {
            console.log('实际分辨率:', videoRef.current.videoWidth, videoRef.current.videoHeight)
          }
          setStream(mediaStream)
          setSelectedCamera(targetDevice)
        }
      }
    } catch (err) {
      console.error('摄像头访问失败:', err)
      showToast('摄像头访问失败', 'error')
    }
  }

  const takePhoto = async () => {
    const video = videoRef.current
    if (!video || !video.readyState === video.HAVE_ENOUGH_DATA) {
      console.error('Video not ready')
      return
    }

    try {
      setProcessing(true) // 开始处理

      const canvas = document.createElement('canvas')
      const videoWidth = video.videoWidth
      const videoHeight = video.videoHeight

      if (mode === 'document') {
        // 优化1：使用更合理的文档尺寸
        const targetWidth = 1280 // 降低目标分辨率提升性能
        const targetHeight = (targetWidth * videoHeight) / videoWidth

        canvas.width = targetWidth
        canvas.height = targetHeight

        const context = canvas.getContext('2d')
        context.drawImage(video, 0, 0, videoWidth, videoHeight, 0, 0, targetWidth, targetHeight)

        // 直接生成数据URL，避免不必要的Blob转换
        const imageData = canvas.toDataURL('image/jpeg', 0.85) // 使用0.85质量平衡大小和质量

        const today = new Date()
        const dateDir = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`

        setPreviewImage({
          data: imageData,
          fileName: `${dateDir}/photo_${mode}_${Date.now()}.jpg`
        })

        setShowPreview(true)
      } else {
        // 证件和人像模式：使用裁剪
        // 获取当前实际的拍照模式（身份证需要区分正反面）
        const currentMode = mode === 'idcard' ? `idcard-${idCardMode}` : mode
        const cropStyle = cropStyles[currentMode]
        const cropWidthPercent = parseFloat(cropStyle.width) / 100
        const cropHeightPercent = parseFloat(cropStyle.height) / 100

        // 计算裁剪区域的实际尺寸和位置
        const cropWidth = videoWidth * cropWidthPercent
        const cropHeight = videoHeight * cropHeightPercent
        const cropX = (videoWidth - cropWidth) / 2 // 居中裁剪
        const cropY = (videoHeight - cropHeight) / 2

        // 设置画布尺寸为裁剪后的尺寸
        canvas.width = cropWidth
        canvas.height = cropHeight

        const context = canvas.getContext('2d')
        context.imageSmoothingEnabled = true
        context.imageSmoothingQuality = 'high'

        // 使用正确的裁剪参数
        context.drawImage(
          video,
          cropX,
          cropY,
          cropWidth,
          cropHeight, // 源图像的裁剪区域
          0,
          0,
          cropWidth,
          cropHeight // 目标画布的绘制区域
        )

        // 使用更高的图片质量
        const imageData = canvas.toDataURL('image/jpeg', 0.9)

        // 生成日期目录格式
        const today = new Date()
        const dateDir = `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`

        // 根据当前模式设置照片类型，身份证需要区分正反面
        const photoType = mode === 'idcard' ? `idcard-${idCardMode}` : mode

        setPreviewImage({
          data: imageData,
          fileName: `${dateDir}/photo_${photoType}_${Date.now()}.jpg`
        })
        setShowPreview(true)
      }
    } catch (error) {
      console.error('拍照失败:', error)
      showToast('拍照失败', 'error')
    } finally {
      setProcessing(false) // 结束处理
    }
  }

  // 定义拍摄模式的顺序
  const shootingModeOrder = ['document', 'idcard', 'portrait']

  // 获取下一个拍摄模式
  const getNextMode = (currentMode) => {
    const currentIndex = shootingModeOrder.indexOf(currentMode)
    if (currentIndex < shootingModeOrder.length - 1) {
      return shootingModeOrder[currentIndex + 1]
    }
    return null // 已经是最后一个模式
  }

  // 取消自动切换
  const cancelAutoSwitch = () => {
    if (autoSwitchTimer) {
      clearTimeout(autoSwitchTimer)
      setAutoSwitchTimer(null)
    }
  }

  // 启动自动切换（静默模式）
  const startAutoSwitch = (targetMode, delay = 1500) => {
    // 先取消之前的定时器
    cancelAutoSwitch()

    const switchTimer = setTimeout(() => {
      // 执行模式切换
      setMode(targetMode)
      if (targetMode === 'idcard') {
        setIdCardMode('front') // 重置身份证模式为正面
      }
      setupCamera(null, targetMode)

      // 清理定时器引用
      setAutoSwitchTimer(null)
    }, delay)

    setAutoSwitchTimer(switchTimer)
  }

  // 修改 handleSave 函数
  const handleSave = () => {
    if (previewImage) {
      onCapture(previewImage)
      // 更新拍照状态 - 身份证需要区分正反面
      const photoKey = mode === 'idcard' ? `idcard-${idCardMode}` : mode
      const updatedPhotosTaken = {
        ...photosTaken,
        [photoKey]: true
      }
      setPhotosTaken(updatedPhotosTaken)

      // 检查是否所有照片都已完成（使用更新后的状态）
      const allCompleted = Object.values(updatedPhotosTaken).every((taken) => taken)

      // 身份证拍照完成后的逻辑
      if (mode === 'idcard') {
        if (idCardMode === 'front') {
          // 拍完正面，自动切换到反面
          showToast('身份证正面拍照完成', 'success')
          setTimeout(() => {
            setIdCardMode('back')
          }, 1500)
        } else {
          // 拍完反面，检查是否需要跳转到下一个模式
          showToast('身份证反面拍照完成', 'success')
          const nextMode = getNextMode('idcard')
          if (nextMode) {
            startAutoSwitch(nextMode)
          } else if (allCompleted) {
            // 所有拍摄任务完成，自动调用完成回调
            onPhotoComplete()
          }
        }
      } else {
        // 其他模式拍照完成后的逻辑
        showToast('照片保存成功', 'success')
        const nextMode = getNextMode(mode)
        if (nextMode) {
          startAutoSwitch(nextMode)
        } else if (allCompleted) {
          // 所有拍摄任务完成，自动调用完成回调
          onPhotoComplete()
        }
      }

      // 关闭预览
      setShowPreview(false)
      setPreviewImage(null)
      setCountdown(5)
      setIsCountingDown(false)
    }
  }

  // 添加 useEffect 来初始化摄像头
  useEffect(() => {
    setupCamera(null, mode)
    return () => {
      if (stream) {
        stream.getTracks().forEach((track) => track.stop())
      }
    }
  }, []) // 组件加载时初始化

  // 添加倒计时效果
  useEffect(() => {
    let timer
    if (isCountingDown && countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1)
      }, 1000)
    } else if (countdown === 0) {
      takePhoto()
    }
    return () => {
      if (timer) clearInterval(timer)
    }
  }, [isCountingDown, countdown])

  // 添加对 showPreview 的监听
  useEffect(() => {
    if (!showPreview) {
      // 当退出预览模式时，确保摄像头已经初始化
      setupCamera(selectedCamera, mode)
    }
  }, [showPreview])

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (autoSwitchTimer) {
        clearTimeout(autoSwitchTimer)
      }
    }
  }, [autoSwitchTimer])

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
      {/* 处理状态遮罩 */}
      {processing && (
        <div className="absolute inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
          <div className="text-white text-xl animate-pulse">处理中，请稍候...</div>
        </div>
      )}

      <div className="bg-white rounded-2xl p-4 w-full relative">
        {/* 拍照进度指示器 - 始终展开状态 */}
        <div className="absolute top-1/2 -translate-y-1/2 left-8 z-10 bg-white/40 backdrop-blur-sm rounded-xl p-4 m-2 shadow-lg">
          <div className="space-y-3">
            {Object.entries(photosTaken).map(([type, taken]) => {
              // 为身份证正反面提供更清晰的显示名称
              let displayName = ''
              let titleText = ''

              if (type === 'document') {
                displayName = '文档'
                titleText = '文档照片'
              } else if (type === 'idcard-front') {
                displayName = '身份证正面'
                titleText = '身份证正面照片'
              } else if (type === 'idcard-back') {
                displayName = '身份证反面'
                titleText = '身份证反面照片'
              } else if (type === 'portrait') {
                displayName = '人像'
                titleText = '人像照片'
              }

              return (
                <div key={type} className="flex items-center gap-2" title={titleText}>
                  <svg
                    className={`w-5 h-5 ${taken ? 'text-green-500' : 'text-gray-300'}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    {taken ? (
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    ) : (
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    )}
                  </svg>
                  <span className={`text-sm ${taken ? 'text-green-600' : 'text-gray-500'}`}>
                    {displayName}
                  </span>
                </div>
              )
            })}
          </div>

          {/* 许可申请按钮 */}
          {allPhotosTaken && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <button onClick={onPhotoComplete} className="btn-primary">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                <span>{menuLabel}</span>
              </button>
            </div>
          )}
        </div>

        <div className="relative">
          {showPreview ? (
            // 预览界面
            <div className="flex flex-col items-center w-full max-w-5xl mx-auto">
              <div className="w-full bg-white rounded-2xl shadow-2xl overflow-hidden">
                <div className="bg-gradient-to-r from-teal-500 to-emerald-500 p-4 text-white">
                  <h2 className="text-2xl font-semibold text-center">
                    {mode === 'document'
                      ? '文档照片预览'
                      : mode === 'idcard'
                        ? `身份证${idCardMode === 'front' ? '正面' : '反面'}预览`
                        : '人像照片预览'}
                  </h2>
                  <p className="text-sm opacity-80 text-center mt-1">
                    {new Date().toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
                <div className="relative bg-gradient-to-b from-gray-50 to-gray-100 p-8 rounded-b-2xl">
                  <img
                    src={previewImage.data}
                    alt="预览"
                    className="w-full h-full object-contain rounded-lg shadow-lg"
                  />
                </div>
              </div>
              <div className="mt-6 flex justify-center gap-6">
                <button onClick={handleSave} className="btn-success">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  保存照片
                </button>
                <button
                  onClick={async () => {
                    setShowPreview(false)
                    setPreviewImage(null)
                    setCountdown(5)
                    setIsCountingDown(false) // 重置倒计时状态
                    // 重新初始化摄像头
                    await setupCamera(selectedCamera, mode)
                  }}
                  className="btn-secondary"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  重新拍摄
                </button>
              </div>
            </div>
          ) : (
            // 拍照界面
            <>
              {/* 摄像头预览区域 */}
              <div style={videoContainerStyle}>
                <video ref={videoRef} autoPlay playsInline style={videoStyle} />

                {/* 裁剪框 - 支持身份证和人像模式 */}
                {(mode === 'idcard' || mode === 'portrait') && (
                  <>
                    {/* 获取当前实际的裁剪样式 */}
                    {(() => {
                      const currentCropMode = mode === 'idcard' ? `idcard-${idCardMode}` : mode
                      const currentCropStyle = cropStyles[currentCropMode]

                      return (
                        <>
                          {/* 添加模糊遮罩 */}
                          <div className="absolute inset-0">
                            {/* 上方模糊区域 */}
                            <div
                              className="absolute backdrop-blur-sm bg-black/30"
                              style={{
                                top: 0,
                                left: 0,
                                right: 0,
                                height: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`
                              }}
                            />
                            {/* 下方模糊区域 */}
                            <div
                              className="absolute backdrop-blur-sm bg-black/30"
                              style={{
                                bottom: 0,
                                left: 0,
                                right: 0,
                                height: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`
                              }}
                            />
                            {/* 左侧模糊区域 */}
                            <div
                              className="absolute backdrop-blur-sm bg-black/30"
                              style={{
                                top: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`,
                                bottom: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`,
                                left: 0,
                                width: `calc(50% - ${parseFloat(currentCropStyle.width) / 2}%)`
                              }}
                            />
                            {/* 右侧模糊区域 */}
                            <div
                              className="absolute backdrop-blur-sm bg-black/30"
                              style={{
                                top: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`,
                                bottom: `calc(50% - ${parseFloat(currentCropStyle.height) / 2}%)`,
                                right: 0,
                                width: `calc(50% - ${parseFloat(currentCropStyle.width) / 2}%)`
                              }}
                            />
                          </div>

                          {/* 裁剪框 */}
                          <div
                            className="absolute border-2 border-yellow-400 border-dashed pointer-events-none z-10"
                            style={{
                              width: currentCropStyle.width,
                              height: currentCropStyle.height,
                              top: '50%',
                              left: '50%',
                              transform: 'translate(-50%, -50%)',
                              transition: 'all 0.3s ease'
                            }}
                          >
                            {/* 四角标记 */}
                            <div className="absolute top-0 left-0 w-6 h-6 border-l-4 border-t-4 border-white"></div>
                            <div className="absolute top-0 right-0 w-6 h-6 border-r-4 border-t-4 border-white"></div>
                            <div className="absolute bottom-0 left-0 w-6 h-6 border-l-4 border-b-4 border-white"></div>
                            <div className="absolute bottom-0 right-0 w-6 h-6 border-r-4 border-b-4 border-white"></div>

                            {/* 添加辅助线 */}
                            <div className="absolute top-1/3 left-0 right-0 border-t border-white opacity-30"></div>
                            <div className="absolute top-2/3 left-0 right-0 border-t border-white opacity-30"></div>
                            <div className="absolute left-1/3 top-0 bottom-0 border-l border-white opacity-30"></div>
                            <div className="absolute left-2/3 top-0 bottom-0 border-l border-white opacity-30"></div>

                            {/* 添加提示文字 - 根据身份证正反面显示不同提示 */}
                            <div
                              className="absolute -top-8 left-1/2 transform -translate-x-1/2
                                            text-white text-sm px-2 py-1 rounded bg-black bg-opacity-50"
                            >
                              {mode === 'idcard'
                                ? `请将身份证${idCardMode === 'front' ? '正面' : '反面'}放入框内`
                                : '请将头部放入框内'}
                            </div>
                          </div>
                        </>
                      )
                    })()}
                  </>
                )}

                {/* 倒计时显示保持不变 */}
                {isCountingDown && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-9xl font-bold text-white drop-shadow-[0_0_10px_rgba(0,0,0,0.5)]">
                      {countdown}
                    </div>
                  </div>
                )}
              </div>

              {/* 操作按钮区域，添加模式切换按钮 */}
              {!isCountingDown && (
                <div className="mt-6 flex flex-col items-center gap-4">
                  {/* 主要模式切换按钮组 */}
                  <div className="flex justify-center gap-3 mb-2">
                    <button
                      onClick={() => {
                        cancelAutoSwitch() // 取消自动切换
                        setMode('document')
                        setupCamera(null, 'document')
                      }}
                      className={`btn-outline ${mode === 'document' ? 'active' : ''}`}
                    >
                      文档模式
                    </button>
                    <button
                      onClick={() => {
                        cancelAutoSwitch() // 取消自动切换
                        setMode('idcard')
                        setIdCardMode('front') // 重置为正面
                        setupCamera(null, 'idcard')
                      }}
                      className={`btn-outline ${mode === 'idcard' ? 'active' : ''}`}
                    >
                      身份证模式
                    </button>
                    <button
                      onClick={() => {
                        cancelAutoSwitch() // 取消自动切换
                        setMode('portrait')
                        setupCamera(null, 'portrait')
                      }}
                      className={`btn-outline ${mode === 'portrait' ? 'active' : ''}`}
                    >
                      人像模式
                    </button>
                  </div>

                  {/* 身份证正反面切换按钮 */}
                  {mode === 'idcard' && (
                    <div className="flex justify-center gap-2 mb-2">
                      <button
                        onClick={() => setIdCardMode('front')}
                        className={`px-4 py-2 rounded-lg text-sm transition-all duration-300 ${
                          idCardMode === 'front'
                            ? 'bg-blue-500 text-white shadow-md'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        正面
                      </button>
                      <button
                        onClick={() => setIdCardMode('back')}
                        className={`px-4 py-2 rounded-lg text-sm transition-all duration-300 ${
                          idCardMode === 'back'
                            ? 'bg-blue-500 text-white shadow-md'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        反面
                      </button>
                    </div>
                  )}

                  {/* 拍照和取消按钮 */}
                  <div className="flex justify-center gap-6">
                    <button onClick={() => setIsCountingDown(true)} className="btn-primary">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-12"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                      开始拍照
                    </button>
                    <button onClick={onClose} className="btn-secondary">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                      取消
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}

// 添加淡入动画
const style = document.createElement('style')
style.textContent = `
  @keyframes fade-in {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fade-in {
    animation: fade-in 0.2s ease-out;
  }
`
document.head.appendChild(style)

export default Camera
